{"compilerOptions": {"target": "es2022", "module": "esnext", "lib": ["esnext", "dom", "dom.iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "types": ["chrome-types"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "vite.config.ts", "vite.config.background.ts", "vite.config.content.ts"]}